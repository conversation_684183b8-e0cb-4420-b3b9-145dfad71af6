#!/usr/bin/env python3
"""
Whisper语音识别功能测试

测试Whisper实现的各项功能
"""

import os
import tempfile
import numpy as np
import wave
import time
from whisper_speech_recognition import (
    WhisperSpeechRecognizer,
    transcribe_audio,
    transcribe_whisper,
    WHISPER_MODELS,
    SUPPORTED_LANGUAGES
)


def create_test_audio(duration=3.0, frequency=440, sample_rate=16000):
    """创建测试音频文件"""
    # 生成正弦波
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # 添加一些变化使其更像语音
    envelope = np.exp(-t / (duration * 0.5))
    audio_data = audio_data * envelope * 0.5
    
    # 转换为16位整数
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
        temp_path = temp_file.name
    
    # 写入WAV文件
    with wave.open(temp_path, 'w') as wav_file:
        wav_file.setnchannels(1)
        wav_file.setsampwidth(2)
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    return temp_path


def test_basic_functionality():
    """测试基本功能"""
    print("🧪 基本功能测试")
    print("=" * 50)
    
    # 1. 测试初始化
    print("1. 测试初始化...")
    recognizer = WhisperSpeechRecognizer(model_size="tiny")
    print(f"   ✅ 初始化成功，设备: {recognizer.device}")
    
    # 2. 测试模型信息
    print("\n2. 测试模型信息...")
    models = recognizer.get_available_models()
    print(f"   ✅ 可用模型: {len(models)} 个")
    for name in ["tiny", "base", "small"]:
        if name in models:
            info = models[name]
            print(f"      {name}: {info['size']}, {info['accuracy']}")
    
    # 3. 测试语言支持
    print("\n3. 测试语言支持...")
    print(f"   ✅ 支持语言: {len(SUPPORTED_LANGUAGES)} 种")
    for code, name in list(SUPPORTED_LANGUAGES.items())[:5]:
        print(f"      {code}: {name}")
    
    # 4. 测试设备检测
    print("\n4. 测试设备检测...")
    device = recognizer._get_optimal_device()
    print(f"   ✅ 最优设备: {device}")
    
    return recognizer


def test_audio_processing():
    """测试音频处理"""
    print("\n🎵 音频处理测试")
    print("=" * 50)
    
    recognizer = WhisperSpeechRecognizer(model_size="tiny")
    
    # 创建测试音频
    print("1. 创建测试音频...")
    test_audio = create_test_audio(duration=2.0)
    print(f"   ✅ 测试音频: {test_audio}")
    
    try:
        # 测试音频信息获取
        print("\n2. 测试音频信息获取...")
        audio_info = recognizer.get_audio_info(test_audio)
        print(f"   ✅ 音频时长: {audio_info.get('duration', 'N/A')}秒")
        print(f"   ✅ 采样率: {audio_info.get('frame_rate', 'N/A')}Hz")
        print(f"   ✅ 文件大小: {audio_info.get('file_size', 0) / 1024:.1f}KB")
        
        # 测试音频格式转换
        print("\n3. 测试音频格式转换...")
        converted = recognizer._convert_audio_format(test_audio)
        print(f"   ✅ 转换完成: {converted}")
        
        # 清理转换文件
        if converted != test_audio and os.path.exists(converted):
            os.unlink(converted)
            
    finally:
        # 清理测试文件
        if os.path.exists(test_audio):
            os.unlink(test_audio)


def test_model_management():
    """测试模型管理"""
    print("\n🧠 模型管理测试")
    print("=" * 50)
    
    recognizer = WhisperSpeechRecognizer(model_size="tiny")
    
    # 1. 测试模型切换
    print("1. 测试模型切换...")
    original_model = recognizer.model_size
    recognizer.switch_model("base")
    print(f"   ✅ 模型切换: {original_model} -> {recognizer.model_size}")
    
    # 2. 测试处理时间估算
    print("\n2. 测试处理时间估算...")
    durations = [30, 120, 600]
    for duration in durations:
        estimated = recognizer.estimate_processing_time(duration, "tiny")
        print(f"   {duration}秒音频 -> 预计{estimated:.2f}秒")
    
    # 3. 测试模型推荐
    print("\n3. 测试模型推荐...")
    test_durations = [20, 180, 900]
    for duration in test_durations:
        recommended = recognizer.get_optimal_model_for_duration(duration)
        print(f"   {duration}秒音频 -> 推荐{recommended}模型")
    
    # 4. 测试缓存管理
    print("\n4. 测试缓存管理...")
    cache_info = recognizer.get_model_cache_info()
    print(f"   ✅ 缓存模型: {len(cache_info['cached_models'])} 个")
    
    recognizer.clear_model_cache()
    cache_info_after = recognizer.get_model_cache_info()
    print(f"   ✅ 清理后: {len(cache_info_after['cached_models'])} 个")


def test_api_functions():
    """测试API函数"""
    print("\n🔗 API函数测试")
    print("=" * 50)
    
    # 测试函数存在性
    print("1. 测试函数存在性...")
    functions = [
        ("transcribe_audio", transcribe_audio),
        ("transcribe_whisper", transcribe_whisper),
    ]
    
    for name, func in functions:
        print(f"   ✅ {name}: 可用")
    
    # 测试参数验证
    print("\n2. 测试参数验证...")
    recognizer = WhisperSpeechRecognizer()
    
    # 测试语言代码验证
    test_languages = ["auto", "zh", "en", "cn", "chinese"]
    for lang in test_languages:
        validated = recognizer._validate_language_code(lang)
        print(f"   {lang} -> {validated}")


def test_error_handling():
    """测试错误处理"""
    print("\n⚠️ 错误处理测试")
    print("=" * 50)
    
    recognizer = WhisperSpeechRecognizer(model_size="tiny")
    
    # 1. 测试无效URL
    print("1. 测试无效URL处理...")
    try:
        recognizer._download_audio_from_url("")
        print("   ❌ 应该抛出异常")
    except Exception as e:
        print(f"   ✅ 正确处理: {type(e).__name__}")
    
    # 2. 测试无效模型
    print("\n2. 测试无效模型处理...")
    try:
        recognizer.switch_model("invalid_model")
        print("   ❌ 应该抛出异常")
    except Exception as e:
        print(f"   ✅ 正确处理: {type(e).__name__}")
    
    # 3. 测试不存在的文件
    print("\n3. 测试不存在文件处理...")
    try:
        audio_info = recognizer.get_audio_info("nonexistent.wav")
        if "error" in audio_info:
            print("   ✅ 正确返回错误信息")
        else:
            print("   ❌ 应该返回错误")
    except Exception as e:
        print(f"   ✅ 正确处理: {type(e).__name__}")


def test_performance():
    """测试性能"""
    print("\n⚡ 性能测试")
    print("=" * 50)
    
    recognizer = WhisperSpeechRecognizer(model_size="tiny")
    
    # 测试模型加载时间
    print("1. 测试模型加载性能...")
    start_time = time.time()
    try:
        model = recognizer._load_model("tiny")
        load_time = time.time() - start_time
        print(f"   ✅ tiny模型加载时间: {load_time:.2f}秒")
    except Exception as e:
        print(f"   ⚠️ 模型加载失败: {e}")
    
    # 测试缓存性能
    print("\n2. 测试缓存性能...")
    start_time = time.time()
    try:
        model = recognizer._load_model("tiny")  # 应该使用缓存
        cache_time = time.time() - start_time
        print(f"   ✅ 缓存访问时间: {cache_time:.3f}秒")
    except Exception as e:
        print(f"   ⚠️ 缓存访问失败: {e}")


def test_real_transcription():
    """测试真实转录（如果可能）"""
    print("\n🎙️ 真实转录测试")
    print("=" * 50)
    
    # 创建一个更长的测试音频
    test_audio = create_test_audio(duration=5.0, frequency=880)
    
    try:
        print("1. 测试基本转录...")
        result = transcribe_audio(
            audio_path=test_audio,
            language="auto",
            model_size="tiny"
        )
        
        if result:
            print(f"   ✅ 转录成功")
            print(f"   📝 结果类型: {type(result)}")
            print(f"   📝 包含字段: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
            if isinstance(result, dict) and "transcription" in result:
                text = result["transcription"]
                print(f"   📝 文本长度: {len(text)} 字符")
        else:
            print("   ⚠️ 转录返回None")
            
    except Exception as e:
        print(f"   ⚠️ 转录失败: {e}")
        
    finally:
        # 清理测试文件
        if os.path.exists(test_audio):
            os.unlink(test_audio)


def main():
    """主测试函数"""
    print("🎙️ Whisper语音识别功能测试")
    print("=" * 70)
    
    try:
        # 运行各项测试
        recognizer = test_basic_functionality()
        test_audio_processing()
        test_model_management()
        test_api_functions()
        test_error_handling()
        test_performance()
        test_real_transcription()
        
        print("\n" + "=" * 70)
        print("🎉 所有测试完成！")
        
        # 显示系统信息
        cache_info = recognizer.get_model_cache_info()
        print(f"\n📊 系统信息:")
        print(f"   计算设备: {cache_info['device']}")
        print(f"   可用模型: {len(WHISPER_MODELS)} 个")
        print(f"   支持语言: {len(SUPPORTED_LANGUAGES)} 种")
        print(f"   缓存模型: {len(cache_info['cached_models'])} 个")
        
        print(f"\n💡 Whisper语音识别已准备就绪！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
