#!/usr/bin/env python3
"""
基于OpenAI Whisper的语音识别实现

替换原有的讯飞语音识别，保持API兼容性
支持多种音频格式和Whisper模型大小选择

功能特性:
- 支持多种音频格式 (MP3, WAV, M4A, FLAC, AAC等)
- 支持多种语言自动检测和指定
- 支持不同Whisper模型大小 (tiny, base, small, medium, large)
- 保持与原有API的兼容性
- 智能错误处理和重试机制
- 本地处理，无需网络API调用

使用方法:
1. 安装依赖: pip install -r requirements.txt
2. 运行: python whisper_speech_recognition.py
"""

import os
import json
import time
import tempfile
import warnings
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Union, List
from urllib.parse import urlparse
from urllib.request import urlretrieve
from urllib.error import URLError, HTTPError
import socket

# 抑制一些不必要的警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

try:
    import whisper
    import torch
except ImportError as e:
    print(f"❌ 缺少必要依赖: {e}")
    print("💡 请运行: pip install openai-whisper torch")
    raise

try:
    from pydub import AudioSegment
    from pydub.utils import which
except ImportError:
    print("⚠️ 音频处理功能受限，建议安装: pip install pydub")
    AudioSegment = None

# Whisper模型配置
WHISPER_MODELS = {
    "tiny": {"size": "39 MB", "speed": "~32x", "accuracy": "低"},
    "base": {"size": "74 MB", "speed": "~16x", "accuracy": "中等"},
    "small": {"size": "244 MB", "speed": "~6x", "accuracy": "良好"},
    "medium": {"size": "769 MB", "speed": "~2x", "accuracy": "很好"},
    "large": {"size": "1550 MB", "speed": "~1x", "accuracy": "最佳"},
    "large-v2": {"size": "1550 MB", "speed": "~1x", "accuracy": "最佳(v2)"},
    "large-v3": {"size": "1550 MB", "speed": "~1x", "accuracy": "最佳(v3)"}
}

# 语言代码映射（讯飞 -> Whisper）
LANGUAGE_MAPPING = {
    "cn": "zh",  # 中文
    "en": "en",  # 英文
    "ja": "ja",  # 日语
    "ko": "ko",  # 韩语
    "fr": "fr",  # 法语
    "de": "de",  # 德语
    "es": "es",  # 西班牙语
    "ru": "ru",  # 俄语
    "ar": "ar",  # 阿拉伯语
    "hi": "hi",  # 印地语
}

# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = {
    '.mp3', '.wav', '.m4a', '.flac', '.aac', '.ogg', '.wma'
}

# 自定义异常类
class WhisperError(Exception):
    """Whisper语音识别基础异常"""
    pass

class ModelLoadError(WhisperError):
    """模型加载异常"""
    pass

class AudioProcessingError(WhisperError):
    """音频处理异常"""
    pass

class NetworkError(WhisperError):
    """网络相关异常"""
    pass

class FileFormatError(WhisperError):
    """文件格式异常"""
    pass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WhisperSpeechRecognizer:
    """基于Whisper的语音识别器"""
    
    def __init__(self, model_size: str = "base", device: Optional[str] = None):
        """
        初始化Whisper语音识别器
        
        Args:
            model_size: Whisper模型大小 (tiny, base, small, medium, large)
            device: 计算设备 (cuda, cpu, auto)
        """
        self.model_size = model_size
        self.device = device or self._get_optimal_device()
        self.model = None
        self._model_cache = {}
        
        print(f"🎙️ 初始化Whisper语音识别器")
        print(f"📊 模型大小: {model_size}")
        print(f"💻 计算设备: {self.device}")
        
    def _get_optimal_device(self) -> str:
        """获取最优计算设备"""
        if torch.cuda.is_available():
            return "cuda"
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            return "mps"  # Apple Silicon
        else:
            return "cpu"
    
    def _load_model(self, model_size: Optional[str] = None) -> whisper.Whisper:
        """加载Whisper模型（带缓存和错误处理）"""
        model_size = model_size or self.model_size

        if model_size in self._model_cache:
            logger.info(f"使用缓存的模型: {model_size}")
            return self._model_cache[model_size]

        if model_size not in WHISPER_MODELS:
            logger.warning(f"不支持的模型大小: {model_size}，使用默认模型: base")
            model_size = "base"

        print(f"📥 加载Whisper模型: {model_size}")
        model_info = WHISPER_MODELS[model_size]
        print(f"   大小: {model_info['size']}, 速度: {model_info['speed']}, 准确性: {model_info['accuracy']}")

        max_retries = 3
        for attempt in range(max_retries):
            try:
                model = whisper.load_model(model_size, device=self.device)
                self._model_cache[model_size] = model
                print(f"✅ 模型加载成功")
                logger.info(f"模型 {model_size} 加载成功")
                return model

            except torch.cuda.OutOfMemoryError as e:
                logger.error(f"GPU内存不足: {e}")
                if self.device == "cuda":
                    print("⚠️ GPU内存不足，切换到CPU")
                    self.device = "cpu"
                    continue
                else:
                    raise ModelLoadError(f"内存不足，无法加载模型 {model_size}")

            except Exception as e:
                logger.error(f"模型加载失败 (尝试 {attempt + 1}/{max_retries}): {e}")

                if attempt == max_retries - 1:
                    # 最后一次尝试，降级到更小的模型
                    if model_size != "tiny":
                        print(f"🔄 尝试加载更小的模型: tiny")
                        return self._load_model("tiny")
                    else:
                        raise ModelLoadError(f"无法加载任何模型: {e}")

                # 等待后重试
                time.sleep(2 ** attempt)

        raise ModelLoadError(f"模型加载失败，已重试 {max_retries} 次")
    
    def _download_audio_from_url(self, audio_url: str, max_retries: int = 3) -> str:
        """从URL下载音频文件到临时目录（带重试和错误处理）"""
        if not audio_url or not audio_url.strip():
            raise NetworkError("音频URL为空")

        # 验证URL格式
        try:
            parsed_url = urlparse(audio_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise NetworkError(f"无效的URL格式: {audio_url}")
        except Exception as e:
            raise NetworkError(f"URL解析失败: {e}")

        print(f"📥 下载音频文件: {audio_url}")
        logger.info(f"开始下载音频: {audio_url}")

        # 获取文件扩展名
        file_extension = Path(parsed_url.path).suffix.lower()
        if not file_extension or file_extension not in SUPPORTED_AUDIO_FORMATS:
            file_extension = '.mp3'  # 默认扩展名
            logger.warning(f"未知音频格式，使用默认扩展名: {file_extension}")

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=file_extension, delete=False) as temp_file:
            temp_path = temp_file.name

        # 重试下载
        for attempt in range(max_retries):
            try:
                # 设置超时和用户代理
                import urllib.request
                opener = urllib.request.build_opener()
                opener.addheaders = [('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')]
                urllib.request.install_opener(opener)

                # 下载文件
                urlretrieve(audio_url, temp_path)

                # 验证文件
                file_size = os.path.getsize(temp_path)
                if file_size == 0:
                    raise NetworkError("下载的文件为空")

                if file_size < 1024:  # 小于1KB可能是错误页面
                    raise NetworkError("下载的文件过小，可能不是有效的音频文件")

                print(f"✅ 音频下载成功: {file_size / 1024 / 1024:.2f} MB")
                logger.info(f"音频下载成功，文件大小: {file_size} bytes")
                return temp_path

            except (URLError, HTTPError, socket.timeout) as e:
                logger.warning(f"下载失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    # 清理失败的临时文件
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                    raise NetworkError(f"音频下载失败，已重试 {max_retries} 次: {e}")

                # 等待后重试
                time.sleep(2 ** attempt)

            except Exception as e:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                logger.error(f"下载过程中发生未知错误: {e}")
                raise NetworkError(f"音频下载失败: {e}")

        raise NetworkError("下载失败，已达到最大重试次数")
    
    def _convert_audio_format(self, audio_path: str, target_format: str = "wav") -> str:
        """转换音频格式为Whisper支持的格式"""
        if not AudioSegment:
            # 如果没有pydub，直接返回原路径
            print("⚠️ 未安装pydub，跳过音频格式转换")
            return audio_path

        try:
            file_extension = Path(audio_path).suffix.lower()

            # 如果已经是目标格式，直接返回
            if file_extension == f'.{target_format}':
                return audio_path

            print(f"🔄 转换音频格式: {file_extension} -> .{target_format}")

            # 加载音频
            audio = AudioSegment.from_file(audio_path)

            # 获取音频信息
            duration = len(audio) / 1000.0  # 转换为秒
            channels = audio.channels
            frame_rate = audio.frame_rate

            print(f"📊 音频信息: {duration:.2f}秒, {channels}声道, {frame_rate}Hz")

            # 音频预处理
            audio = self._preprocess_audio(audio)

            # 转换为目标格式
            with tempfile.NamedTemporaryFile(suffix=f'.{target_format}', delete=False) as temp_file:
                output_path = temp_file.name

            # 设置导出参数
            export_params = {"format": target_format}
            if target_format == "wav":
                export_params.update({
                    "parameters": ["-ac", "1", "-ar", "16000"]  # 单声道，16kHz
                })

            audio.export(output_path, **export_params)
            print(f"✅ 音频格式转换成功")

            return output_path

        except Exception as e:
            print(f"⚠️ 音频格式转换失败: {e}，使用原文件")
            return audio_path

    def _preprocess_audio(self, audio: 'AudioSegment') -> 'AudioSegment':
        """音频预处理"""
        try:
            # 转换为单声道
            if audio.channels > 1:
                audio = audio.set_channels(1)
                print("🔄 转换为单声道")

            # 标准化采样率为16kHz（Whisper推荐）
            if audio.frame_rate != 16000:
                audio = audio.set_frame_rate(16000)
                print(f"🔄 采样率标准化: {audio.frame_rate}Hz -> 16000Hz")

            # 音量标准化
            audio = self._normalize_audio_volume(audio)

            # 移除静音段（可选）
            audio = self._remove_silence(audio)

            return audio

        except Exception as e:
            print(f"⚠️ 音频预处理失败: {e}")
            return audio

    def _normalize_audio_volume(self, audio: 'AudioSegment') -> 'AudioSegment':
        """音量标准化"""
        try:
            # 计算当前音量
            current_db = audio.dBFS

            # 目标音量（-20dB，避免过度放大）
            target_db = -20.0

            if current_db < target_db - 10:  # 音量太小
                change_db = target_db - current_db
                audio = audio + change_db
                print(f"🔊 音量增强: {change_db:.1f}dB")
            elif current_db > target_db + 5:  # 音量太大
                change_db = target_db - current_db
                audio = audio + change_db
                print(f"🔉 音量降低: {change_db:.1f}dB")

            return audio

        except Exception as e:
            print(f"⚠️ 音量标准化失败: {e}")
            return audio

    def _remove_silence(self, audio: 'AudioSegment', silence_thresh: int = -40) -> 'AudioSegment':
        """移除开头和结尾的静音段"""
        try:
            # 检测静音
            from pydub.silence import detect_leading_silence

            # 移除开头静音
            start_trim = detect_leading_silence(audio, silence_threshold=silence_thresh)

            # 移除结尾静音
            end_trim = detect_leading_silence(audio.reverse(), silence_threshold=silence_thresh)

            if start_trim > 0 or end_trim > 0:
                duration = len(audio)
                audio = audio[start_trim:duration-end_trim]
                print(f"✂️ 移除静音: 开头{start_trim}ms, 结尾{end_trim}ms")

            return audio

        except Exception as e:
            print(f"⚠️ 静音移除失败: {e}")
            return audio

    def get_audio_info(self, audio_path: str) -> Dict[str, Any]:
        """获取音频文件信息"""
        try:
            if AudioSegment:
                audio = AudioSegment.from_file(audio_path)
                return {
                    "duration": len(audio) / 1000.0,  # 秒
                    "channels": audio.channels,
                    "frame_rate": audio.frame_rate,
                    "sample_width": audio.sample_width,
                    "file_size": os.path.getsize(audio_path),
                    "format": Path(audio_path).suffix.lower()
                }
            else:
                # 基本信息
                return {
                    "file_size": os.path.getsize(audio_path),
                    "format": Path(audio_path).suffix.lower()
                }
        except Exception as e:
            print(f"⚠️ 获取音频信息失败: {e}")
            return {"error": str(e)}
    
    def _map_language_code(self, language: str) -> Optional[str]:
        """映射语言代码（讯飞格式 -> Whisper格式）"""
        return LANGUAGE_MAPPING.get(language, language)
    
    def _cleanup_temp_files(self, *file_paths: str):
        """清理临时文件"""
        for file_path in file_paths:
            try:
                if file_path and os.path.exists(file_path):
                    os.unlink(file_path)
            except Exception as e:
                print(f"⚠️ 清理临时文件失败: {e}")

    def transcribe_audio(
        self,
        audio_path: str,
        language: Optional[str] = None,
        task: str = "transcribe",
        **options
    ) -> Dict[str, Any]:
        """
        转录音频文件

        Args:
            audio_path: 音频文件路径
            language: 指定语言（None为自动检测）
            task: 任务类型 ("transcribe" 或 "translate")
            **options: 其他Whisper选项

        Returns:
            Dict: Whisper转录结果
        """
        model = self._load_model()

        # 设置默认选项
        whisper_options = {
            "language": language,
            "task": task,
            "verbose": False,
            **options
        }

        # 移除None值
        whisper_options = {k: v for k, v in whisper_options.items() if v is not None}

        print(f"🎯 开始{task}任务...")
        if language:
            print(f"📝 指定语言: {language}")
        else:
            print(f"🔍 自动检测语言")

        start_time = time.time()
        result = model.transcribe(audio_path, **whisper_options)
        end_time = time.time()

        processing_time = end_time - start_time
        print(f"⏱️ 处理时间: {processing_time:.2f}秒")
        print(f"📊 检测到的语言: {result.get('language', 'unknown')}")

        return result

    def get_available_models(self) -> Dict[str, Dict[str, str]]:
        """获取可用的Whisper模型信息"""
        return WHISPER_MODELS.copy()

    def estimate_processing_time(self, audio_duration: float, model_size: str = None) -> float:
        """估算处理时间"""
        model_size = model_size or self.model_size

        # 基于模型大小的速度倍数（相对于音频时长）
        speed_multipliers = {
            "tiny": 0.03,    # ~32x faster
            "base": 0.06,    # ~16x faster
            "small": 0.17,   # ~6x faster
            "medium": 0.5,   # ~2x faster
            "large": 1.0,    # ~1x (real-time)
            "large-v2": 1.0,
            "large-v3": 1.0
        }

        multiplier = speed_multipliers.get(model_size, 1.0)

        # 根据设备调整
        if self.device == "cuda":
            multiplier *= 0.3  # GPU加速
        elif self.device == "mps":
            multiplier *= 0.5  # Apple Silicon加速

        return audio_duration * multiplier

    def clear_model_cache(self):
        """清理模型缓存"""
        self._model_cache.clear()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        print("🗑️ 模型缓存已清理")
        logger.info("模型缓存已清理")

    def get_model_cache_info(self) -> Dict[str, Any]:
        """获取模型缓存信息"""
        cache_info = {}
        total_memory = 0

        for model_name, model in self._model_cache.items():
            try:
                # 估算模型内存使用
                param_count = sum(p.numel() for p in model.parameters())
                memory_mb = param_count * 4 / 1024 / 1024  # 假设float32

                cache_info[model_name] = {
                    "loaded": True,
                    "parameters": param_count,
                    "memory_mb": memory_mb,
                    "device": str(next(model.parameters()).device)
                }
                total_memory += memory_mb

            except Exception as e:
                cache_info[model_name] = {
                    "loaded": True,
                    "error": str(e)
                }

        return {
            "cached_models": cache_info,
            "total_memory_mb": total_memory,
            "device": self.device
        }

    def preload_models(self, model_sizes: List[str]):
        """预加载多个模型"""
        print(f"📦 预加载模型: {model_sizes}")

        for model_size in model_sizes:
            try:
                print(f"📥 预加载模型: {model_size}")
                self._load_model(model_size)
            except Exception as e:
                logger.error(f"预加载模型 {model_size} 失败: {e}")
                print(f"❌ 预加载模型 {model_size} 失败: {e}")

        print("✅ 模型预加载完成")

    def switch_model(self, model_size: str):
        """切换当前使用的模型"""
        if model_size not in WHISPER_MODELS:
            raise ValueError(f"不支持的模型大小: {model_size}")

        self.model_size = model_size
        print(f"🔄 切换到模型: {model_size}")
        logger.info(f"切换到模型: {model_size}")

    def get_optimal_model_for_duration(self, audio_duration: float) -> str:
        """根据音频时长推荐最优模型"""
        if audio_duration < 30:  # 短音频
            return "base"
        elif audio_duration < 300:  # 中等长度
            return "small" if self.device == "cuda" else "base"
        elif audio_duration < 1800:  # 长音频
            return "medium" if self.device == "cuda" else "small"
        else:  # 超长音频
            return "large" if self.device == "cuda" else "medium"

# 保持与原有API的兼容性
def transcribe_with_whisper(
    audio_url: Optional[str] = None,
    audio_path: Optional[str] = None,
    language: str = "cn",
    translate_to: Optional[str] = None,
    model_size: str = "base",
    **kwargs
) -> Optional[Dict[str, Any]]:
    """
    使用Whisper进行语音转写的完整流程（兼容原有API）
    
    Args:
        audio_url: 音频文件URL（与原API兼容）
        audio_path: 本地音频文件路径
        language: 语言代码 (cn, en, ja, ko等)
        translate_to: 翻译目标语言（暂不支持，保持兼容性）
        model_size: Whisper模型大小
        **kwargs: 其他参数（保持兼容性）
    
    Returns:
        Dict: {"transcription": text} 或 {"transcription": text, "translation": text}
    """
    recognizer = WhisperSpeechRecognizer(model_size=model_size)
    
    temp_files = []
    try:
        # 处理音频输入
        if audio_url:
            audio_file = recognizer._download_audio_from_url(audio_url)
            temp_files.append(audio_file)
        elif audio_path:
            audio_file = audio_path
        else:
            raise ValueError("必须提供 audio_url 或 audio_path")
        
        # 转换音频格式
        converted_audio = recognizer._convert_audio_format(audio_file)
        if converted_audio != audio_file:
            temp_files.append(converted_audio)
        
        # 加载模型
        model = recognizer._load_model()
        
        # 映射语言代码
        whisper_language = recognizer._map_language_code(language)
        
        print(f"🎯 开始语音识别...")
        print(f"📝 识别语言: {language} -> {whisper_language}")
        
        # 执行语音识别
        result = model.transcribe(
            converted_audio,
            language=whisper_language if whisper_language != "auto" else None,
            task="transcribe"
        )
        
        transcription_text = result["text"].strip()
        
        print(f"✅ 语音识别完成")
        print(f"📊 识别到的语言: {result.get('language', 'unknown')}")
        
        # 构建返回结果（保持与原API兼容）
        response = {"transcription": transcription_text}
        
        # 如果需要翻译（使用Whisper的翻译功能）
        if translate_to and translate_to != language:
            print(f"🌐 开始翻译: {language} -> {translate_to}")
            
            # 使用Whisper的翻译功能（仅支持翻译为英文）
            if translate_to in ["en", "english"]:
                translate_result = model.transcribe(
                    converted_audio,
                    language=whisper_language if whisper_language != "auto" else None,
                    task="translate"  # 翻译任务
                )
                response["translation"] = translate_result["text"].strip()
                print(f"✅ 翻译完成")
            else:
                print(f"⚠️ Whisper仅支持翻译为英文，跳过翻译")
        
        # 保存结果
        save_whisper_results(response, result, audio_file)
        
        return response
        
    except Exception as e:
        print(f"❌ 语音识别失败: {e}")
        return None
    finally:
        # 清理临时文件
        recognizer._cleanup_temp_files(*temp_files)


def save_whisper_results(response: Dict[str, Any], raw_result: Dict[str, Any], audio_file: str):
    """保存Whisper识别结果"""
    try:
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 保存详细结果
        result_data = {
            "transcription": response.get("transcription", ""),
            "translation": response.get("translation"),
            "raw_result": raw_result,
            "timestamp": timestamp,
            "provider": "whisper",
            "audio_file": audio_file
        }
        
        with open("whisper_result.json", "w", encoding="utf-8") as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        # 保存纯文本
        with open("whisper_transcript.txt", "w", encoding="utf-8") as f:
            f.write(response["transcription"])
        
        print(f"📄 识别结果已保存:")
        print(f"   - whisper_result.json (详细结果)")
        print(f"   - whisper_transcript.txt (转录文本)")
        
        if response.get("translation"):
            with open("whisper_translation.txt", "w", encoding="utf-8") as f:
                f.write(response["translation"])
            print(f"   - whisper_translation.txt (翻译文本)")
            
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")


# 为了保持完全兼容，创建别名函数
def transcribe_with_xfyun(audio_url=None, language="cn", translate_to=None, **kwargs):
    """兼容性别名函数，重定向到Whisper实现"""
    return transcribe_with_whisper(
        audio_url=audio_url,
        language=language,
        translate_to=translate_to,
        **kwargs
    )


if __name__ == "__main__":
    # 测试示例
    print("🎙️ Whisper语音识别测试")
    print("=" * 50)
    
    # 测试音频URL
    test_audio_url = "https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241114/mgiguo/asr_example.wav"
    
    # 基本转写测试
    print("\n1. 基本语音转写测试")
    result = transcribe_with_whisper(
        audio_url=test_audio_url,
        language="en",
        model_size="base"
    )
    
    if result:
        print(f"✅ 转写成功: {result['transcription'][:100]}...")
    else:
        print("❌ 转写失败")
    
    print("\n🎉 测试完成！")
