# OpenAI Whisper语音识别核心依赖
openai-whisper>=20231117
torch>=1.9.0
torchaudio>=0.9.0

# 音频处理依赖
pydub>=0.25.1
ffmpeg-python>=0.2.0

# 网络请求和数据处理
requests>=2.25.1
urllib3>=1.26.0

# 数据格式处理
numpy>=1.21.0
scipy>=1.7.0

# 可选依赖 - GPU加速（如果有NVIDIA GPU）
# torch[cuda] - 取消注释以启用CUDA支持

# 可选依赖 - 更好的音频格式支持
# librosa>=0.8.0 - 高级音频分析
# soundfile>=0.10.0 - 更多音频格式支持

# 开发和测试依赖
pytest>=6.0.0
pytest-cov>=2.10.0

# 兼容性依赖（保持与原有代码的兼容）
hashlib2>=1.0.0  # 如果需要特定的哈希算法
hmac>=20101005   # HMAC签名支持
