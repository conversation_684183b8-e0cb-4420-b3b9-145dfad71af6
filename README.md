# Whisper语音识别实现

基于OpenAI Whisper模型的语音识别实现，完全替换原有的讯飞语音识别API，提供更好的隐私保护、成本效益和功能特性。

## 🎯 主要特性

### ✅ 完全兼容原有API
- 保持与原有`transcribe_with_xfyun`函数的完全兼容
- 相同的函数签名和返回值格式
- 无需修改现有代码即可迁移

### 🔒 隐私保护
- **完全本地处理**：数据不离开本地设备
- **无网络依赖**：支持完全离线使用
- **无数据上传**：敏感音频数据安全保障

### 💰 成本效益
- **免费使用**：无API调用费用
- **一次安装**：无限制使用
- **无配额限制**：不受使用量限制

### 🎛️ 灵活配置
- **7种模型大小**：从tiny(39MB)到large(1550MB)
- **智能推荐**：根据音频时长自动推荐最优模型
- **硬件加速**：支持CUDA、MPS、CPU多线程

### 🌍 强大语言支持
- **99种语言**：支持自动语言检测
- **多语言混合**：处理多语言混合音频
- **方言识别**：支持各种方言和口音

## 📦 安装

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 核心依赖
- `openai-whisper`: OpenAI Whisper模型
- `torch`: PyTorch深度学习框架
- `torchaudio`: 音频处理
- `pydub`: 音频格式转换（可选）

## 🚀 快速开始

### 基本使用（兼容原API）
```python
from whisper_speech_recognition import transcribe_with_xfyun

# 与原有API完全相同的调用方式
result = transcribe_with_xfyun(
    audio_url="https://example.com/audio.mp3",
    language="cn"
)

print(result["transcription"])
```

### 使用新的Whisper API
```python
from whisper_speech_recognition import transcribe_with_whisper

# 更多功能的新API
result = transcribe_with_whisper(
    audio_url="https://example.com/audio.mp3",
    language="cn",
    model_size="base",  # 选择模型大小
    translate_to="en"   # 翻译为英文
)

print(f"转录: {result['transcription']}")
if 'translation' in result:
    print(f"翻译: {result['translation']}")
```

### 本地文件处理
```python
result = transcribe_with_whisper(
    audio_path="./local_audio.wav",
    language="auto",  # 自动检测语言
    model_size="small"
)
```

## 🧠 模型选择指南

| 模型 | 大小 | 速度 | 准确性 | 适用场景 |
|------|------|------|--------|----------|
| tiny | 39 MB | ~32x | 低 | 实时应用、移动设备 |
| base | 74 MB | ~16x | 中等 | 平衡性能和准确性 |
| small | 244 MB | ~6x | 良好 | 高质量转录 |
| medium | 769 MB | ~2x | 很好 | 专业应用 |
| large | 1550 MB | ~1x | 最佳 | 最高准确性要求 |

### 自动模型推荐
```python
from whisper_speech_recognition import WhisperSpeechRecognizer

recognizer = WhisperSpeechRecognizer()

# 根据音频时长获取推荐模型
audio_duration = 300  # 5分钟
recommended_model = recognizer.get_optimal_model_for_duration(audio_duration)
print(f"推荐模型: {recommended_model}")
```

## ⚡ 性能优化

### GPU加速
```python
# 自动检测并使用最优设备
recognizer = WhisperSpeechRecognizer(device="auto")  # 默认

# 强制使用GPU
recognizer = WhisperSpeechRecognizer(device="cuda")

# 强制使用CPU
recognizer = WhisperSpeechRecognizer(device="cpu")
```

### 模型缓存管理
```python
# 预加载多个模型
recognizer.preload_models(["tiny", "base", "small"])

# 查看缓存状态
cache_info = recognizer.get_model_cache_info()
print(f"已缓存模型: {len(cache_info['cached_models'])}")

# 清理缓存
recognizer.clear_model_cache()
```

### 批处理
```python
audio_files = ["audio1.wav", "audio2.wav", "audio3.wav"]
results = []

for audio_file in audio_files:
    result = transcribe_with_whisper(
        audio_path=audio_file,
        model_size="base"
    )
    results.append(result)
```

## 🌍 语言支持

### 支持的语言
- **中文** (cn → zh)
- **英文** (en → en)  
- **日语** (ja → ja)
- **韩语** (ko → ko)
- **法语** (fr → fr)
- **德语** (de → de)
- **西班牙语** (es → es)
- **俄语** (ru → ru)
- **阿拉伯语** (ar → ar)
- **印地语** (hi → hi)
- **以及89种其他语言**

### 自动语言检测
```python
result = transcribe_with_whisper(
    audio_path="multilingual_audio.wav",
    language="auto"  # 自动检测
)

print(f"检测到的语言: {result.get('detected_language')}")
```

## 🔧 高级功能

### 音频预处理
```python
recognizer = WhisperSpeechRecognizer()

# 获取音频信息
audio_info = recognizer.get_audio_info("audio.wav")
print(f"时长: {audio_info['duration']}秒")
print(f"采样率: {audio_info['frame_rate']}Hz")

# 音频格式转换
converted_path = recognizer._convert_audio_format("audio.mp3", "wav")
```

### 处理时间估算
```python
# 估算处理时间
audio_duration = 120  # 2分钟音频
estimated_time = recognizer.estimate_processing_time(
    audio_duration, 
    model_size="base"
)
print(f"预计处理时间: {estimated_time:.2f}秒")
```

## 📊 性能对比

| 特性 | Whisper实现 | 讯飞API |
|------|-------------|---------|
| 本地处理 | ✅ 完全本地 | ❌ 需要网络 |
| 成本 | ✅ 免费 | ⚠️ 按量收费 |
| 隐私 | ✅ 数据不离开本地 | ⚠️ 上传到服务器 |
| 离线使用 | ✅ 支持 | ❌ 不支持 |
| 模型选择 | ✅ 7种模型 | ❌ 无选择 |
| 语言支持 | ✅ 99种语言 | ⚠️ 主要语言 |
| 实时处理 | ✅ 支持(小模型) | ❌ 异步处理 |
| GPU加速 | ✅ 支持 | ❌ 服务器端 |

## 🔄 迁移指南

### 从讯飞API迁移

1. **安装新依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **更新导入**
   ```python
   # 原有代码
   from test import transcribe_with_xfyun
   
   # 新代码（保持兼容）
   from whisper_speech_recognition import transcribe_with_xfyun
   ```

3. **移除API配置**
   ```python
   # 删除这些配置
   # XFYUN_CONFIG = {
   #     "appid": "your_appid",
   #     "secret_key": "your_secret_key"
   # }
   ```

4. **测试验证**
   ```bash
   python test_whisper_basic.py
   ```

### 零代码修改迁移
现有使用`transcribe_with_xfyun`的代码无需任何修改即可使用新的Whisper实现。

## 🧪 测试

### 运行基础测试
```bash
python test_whisper_basic.py
```

### 运行性能对比
```bash
python performance_comparison.py
```

### 运行功能演示
```bash
python whisper_demo.py
```

## 📁 文件结构

```
.
├── whisper_speech_recognition.py    # 主实现文件
├── requirements.txt                 # 依赖配置
├── test_whisper_basic.py           # 基础测试
├── test_whisper_speech_recognition.py  # 单元测试
├── performance_comparison.py        # 性能对比
├── whisper_demo.py                 # 功能演示
└── README.md                       # 说明文档
```

## ⚠️ 注意事项

1. **首次使用**：首次使用时会自动下载模型文件，请确保网络连接
2. **存储空间**：模型文件需要一定存储空间（39MB-1550MB）
3. **处理速度**：CPU处理速度较慢，建议使用GPU加速
4. **音频格式**：建议使用WAV格式以获得最佳兼容性

## 🆘 故障排除

### 常见问题

**Q: 模型下载失败**
A: 检查网络连接，或手动下载模型文件

**Q: 处理速度慢**
A: 使用更小的模型（tiny/base）或启用GPU加速

**Q: 音频格式不支持**
A: 安装ffmpeg或使用pydub进行格式转换

**Q: 内存不足**
A: 使用更小的模型或清理模型缓存

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**🎉 现在您可以享受更私密、更经济、更强大的语音识别服务！**
