# Whisper语音识别实现

基于OpenAI Whisper模型的语音识别实现，提供强大的本地语音识别能力，支持99种语言，完全免费且保护隐私。

## 🎯 主要特性

### 🔒 隐私保护
- **完全本地处理**：数据不离开本地设备
- **无网络依赖**：支持完全离线使用
- **无数据上传**：敏感音频数据安全保障

### 💰 成本效益
- **免费使用**：无API调用费用
- **一次安装**：无限制使用
- **无配额限制**：不受使用量限制

### 🎛️ 灵活配置
- **7种模型大小**：从tiny(39MB)到large(1550MB)
- **智能推荐**：根据音频时长自动推荐最优模型
- **硬件加速**：支持CUDA、MPS、CPU多线程

### 🌍 强大语言支持
- **99种语言**：支持自动语言检测
- **多语言混合**：处理多语言混合音频
- **方言识别**：支持各种方言和口音

## 📦 安装

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 核心依赖
- `openai-whisper`: OpenAI Whisper模型
- `torch`: PyTorch深度学习框架
- `torchaudio`: 音频处理
- `pydub`: 音频格式转换（可选）

## 🚀 快速开始

### 基本使用
```python
from whisper_speech_recognition import transcribe_audio

# 转录本地音频文件
result = transcribe_audio(
    audio_path="audio.wav",
    language="auto"  # 自动检测语言
)

print(result["transcription"])
```

### 使用URL音频
```python
from whisper_speech_recognition import transcribe_whisper

# 转录网络音频
result = transcribe_whisper(
    audio_url="https://example.com/audio.mp3",
    language="zh",      # 指定中文
    model_size="base"   # 选择模型大小
)

print(f"转录: {result['transcription']}")
```

### 高级使用
```python
from whisper_speech_recognition import WhisperSpeechRecognizer

# 创建识别器实例
recognizer = WhisperSpeechRecognizer(model_size="base", device="auto")

# 获取音频信息
info = recognizer.get_audio_info("audio.wav")
print(f"音频时长: {info['duration']}秒")

# 执行转录
result = recognizer.transcribe_audio("audio.wav", language="auto")
```

## 🧠 模型选择指南

| 模型 | 大小 | 速度 | 准确性 | 适用场景 |
|------|------|------|--------|----------|
| tiny | 39 MB | ~32x | 低 | 实时应用、移动设备 |
| base | 74 MB | ~16x | 中等 | 平衡性能和准确性 |
| small | 244 MB | ~6x | 良好 | 高质量转录 |
| medium | 769 MB | ~2x | 很好 | 专业应用 |
| large | 1550 MB | ~1x | 最佳 | 最高准确性要求 |

### 自动模型推荐
```python
from whisper_speech_recognition import WhisperSpeechRecognizer

recognizer = WhisperSpeechRecognizer()

# 根据音频时长获取推荐模型
audio_duration = 300  # 5分钟
recommended_model = recognizer.get_optimal_model_for_duration(audio_duration)
print(f"推荐模型: {recommended_model}")
```

## ⚡ 性能优化

### GPU加速
```python
# 自动检测并使用最优设备
recognizer = WhisperSpeechRecognizer(device="auto")  # 默认

# 强制使用GPU
recognizer = WhisperSpeechRecognizer(device="cuda")

# 强制使用CPU
recognizer = WhisperSpeechRecognizer(device="cpu")
```

### 模型缓存管理
```python
# 预加载多个模型
recognizer.preload_models(["tiny", "base", "small"])

# 查看缓存状态
cache_info = recognizer.get_model_cache_info()
print(f"已缓存模型: {len(cache_info['cached_models'])}")

# 清理缓存
recognizer.clear_model_cache()
```

### 批处理
```python
audio_files = ["audio1.wav", "audio2.wav", "audio3.wav"]
results = []

for audio_file in audio_files:
    result = transcribe_audio(
        audio_path=audio_file,
        model_size="base"
    )
    results.append(result)
```

## 🌍 语言支持

### 支持的语言
- **中文** (cn → zh)
- **英文** (en → en)  
- **日语** (ja → ja)
- **韩语** (ko → ko)
- **法语** (fr → fr)
- **德语** (de → de)
- **西班牙语** (es → es)
- **俄语** (ru → ru)
- **阿拉伯语** (ar → ar)
- **印地语** (hi → hi)
- **以及89种其他语言**

### 自动语言检测
```python
result = transcribe_audio(
    audio_path="multilingual_audio.wav",
    language="auto"  # 自动检测
)

print(f"检测到的语言: {result.get('detected_language')}")
```

## 🔧 高级功能

### 音频预处理
```python
recognizer = WhisperSpeechRecognizer()

# 获取音频信息
audio_info = recognizer.get_audio_info("audio.wav")
print(f"时长: {audio_info['duration']}秒")
print(f"采样率: {audio_info['frame_rate']}Hz")

# 音频格式转换
converted_path = recognizer._convert_audio_format("audio.mp3", "wav")
```

### 处理时间估算
```python
# 估算处理时间
audio_duration = 120  # 2分钟音频
estimated_time = recognizer.estimate_processing_time(
    audio_duration, 
    model_size="base"
)
print(f"预计处理时间: {estimated_time:.2f}秒")
```

## 📊 Whisper优势

| 特性 | 说明 |
|------|------|
| 🔒 隐私保护 | 数据完全本地处理，不上传到服务器 |
| 💰 完全免费 | 无API费用，无使用限制 |
| 🌐 离线使用 | 无需网络连接，适合离线环境 |
| 🎛️ 模型选择 | 7种模型大小，从39MB到1550MB |
| 🚀 实时处理 | tiny/base模型支持实时识别 |
| 🌍 多语言 | 支持99种语言，自动检测 |
| ⚡ 硬件加速 | 支持CUDA/MPS/CPU多线程 |
| 🔧 开源实现 | 基于OpenAI Whisper，可自定义 |

## 🚀 快速开始指南

### 安装和设置

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **基本使用**
   ```python
   from whisper_speech_recognition import transcribe_audio

   result = transcribe_audio(
       audio_path="your_audio.wav",
       language="auto"
   )
   print(result["transcription"])
   ```

3. **测试验证**
   ```bash
   python test_whisper.py
   ```

## 🧪 测试

### 运行功能测试
```bash
python test_whisper.py
```

### 查看使用示例
```bash
python example_usage.py
```

### 运行主程序
```bash
python whisper_speech_recognition.py
```

## 📁 文件结构

```
.
├── whisper_speech_recognition.py    # 主实现文件
├── requirements.txt                 # 依赖配置
├── test_whisper.py                 # 功能测试
├── example_usage.py                # 使用示例
└── README.md                       # 说明文档
```

## ⚠️ 注意事项

1. **首次使用**：首次使用时会自动下载模型文件，请确保网络连接
2. **存储空间**：模型文件需要一定存储空间（39MB-1550MB）
3. **处理速度**：CPU处理速度较慢，建议使用GPU加速
4. **音频格式**：建议使用WAV格式以获得最佳兼容性

## 🆘 故障排除

### 常见问题

**Q: 模型下载失败**
A: 检查网络连接，或手动下载模型文件

**Q: 处理速度慢**
A: 使用更小的模型（tiny/base）或启用GPU加速

**Q: 音频格式不支持**
A: 安装ffmpeg或使用pydub进行格式转换

**Q: 内存不足**
A: 使用更小的模型或清理模型缓存

## 📄 许可证

本项目基于MIT许可证开源。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**🎉 现在您可以享受更私密、更经济、更强大的语音识别服务！**
