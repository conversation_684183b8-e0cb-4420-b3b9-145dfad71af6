#!/usr/bin/env python3
"""
讯飞语音转写API工具

使用讯飞开放平台的语音转写服务进行音频文件转写
支持多种语言和音频格式

功能特性:
- 支持多种音频格式 (MP3, WAV, PCM, AAC等)
- 支持多种语言 (中文、英文、日语、韩语等)
- 支持角色分离和热词功能
- 支持回调通知
- 智能错误处理

使用方法:
1. 在讯飞开放平台创建应用: https://console.xfyun.cn/
2. 获取APPID和SECRET_KEY
3. 设置到代码中
4. 运行: python test.py
"""

import requests
import json
import time
import hashlib
import hmac
import base64
from urllib.parse import urlencode
import os

# 讯飞开放平台配置
XFYUN_CONFIG = {
    "appid": "c7d83337",
    "secret_key": "0fb7d422aaa59c7a476e735171281f6c",
}

# 测试音频文件
TEST_AUDIO_URLS = {
    "english": "https://help-static-aliyun-doc.aliyuncs.com/file-manage-files/zh-CN/20241114/mgiguo/asr_example.wav",
    "chinese_mp3": "https://video--tanslate.oss-cn-shanghai.aliyuncs.com/audio/688c348d89bd277b76405586.mp3",
}

# 默认使用中文MP3文件
AUDIO_URL = TEST_AUDIO_URLS["chinese_mp3"]

# API地址
UPLOAD_URL = "https://raasr.xfyun.cn/v2/api/upload"
RESULT_URL = "https://raasr.xfyun.cn/v2/api/getResult"


def generate_signa(appid, secret_key, ts):
    """生成讯飞API签名"""
    # 1. 生成baseString
    base_string = appid + str(ts)

    # 2. 对baseString进行MD5
    md5_string = hashlib.md5(base_string.encode('utf-8')).hexdigest()

    # 3. 使用secret_key对MD5结果进行HmacSHA1加密，然后base64编码
    signa = base64.b64encode(
        hmac.new(secret_key.encode('utf-8'),
                 md5_string.encode('utf-8'), hashlib.sha1).digest()
    ).decode('utf-8')

    return signa


def upload_audio_file(audio_url, language="cn", translate_to=None, **kwargs):
    """
    上传音频文件到讯飞进行转写

    Args:
        audio_url: 音频文件URL
        language: 语言代码 (cn, en, ja, ko等)
        translate_to: 翻译目标语言 (en, cn, ja, ko等)
        **kwargs: 其他参数
    """
    print(f"=== 讯飞语音转写 ===")
    print(f"音频URL: {audio_url}")
    print(f"识别语言: {language}")
    if translate_to:
        print(f"翻译目标: {translate_to}")
    print("=" * 50)

    try:
        # 检查配置
        if XFYUN_CONFIG["appid"] == "your_appid_here":
            print("❌ 请先配置讯飞APPID和SECRET_KEY")
            print("💡 获取方法:")
            print("   1. 访问 https://console.xfyun.cn/")
            print("   2. 创建应用并开通语音转写服务")
            print("   3. 获取APPID和SECRET_KEY")
            return None

        print("🚀 开始上传音频文件...")

        # 生成时间戳和签名
        ts = int(time.time())
        signa = generate_signa(
            XFYUN_CONFIG["appid"], XFYUN_CONFIG["secret_key"], ts)

        # 准备上传参数
        params = {
            "appId": XFYUN_CONFIG["appid"],
            "ts": ts,
            "signa": signa,
            "fileName": "audio_file.mp3",  # 从URL提取或默认
            "fileSize": 1000000,  # 估算大小，实际会被服务器验证
            "duration": 300,  # 估算时长
            "language": language,
            "audioMode": "urlLink",  # 使用URL链接模式
            "audioUrl": audio_url,
            "roleType": kwargs.get("roleType", 0),  # 角色分离
            "pd": kwargs.get("pd", ""),  # 领域个性化
            "hotWord": kwargs.get("hotWord", ""),  # 热词
        }

        # 添加翻译参数
        if translate_to and translate_to != language:
            params["transLanguage"] = translate_to  # 翻译目标语种
            params["transMode"] = kwargs.get("transMode", 2)  # 翻译模式：2=按段落翻译
            print(f"🌐 启用翻译功能: {language} → {translate_to}")

        # 添加句子级别分割参数（针对英文优化）
        if language == "en":
            params["eng_seg_max"] = kwargs.get("eng_seg_max", 30)      # 最大30字符一句
            params["eng_seg_min"] = kwargs.get("eng_seg_min", 5)       # 最小5字符一句
            params["eng_seg_weight"] = kwargs.get("eng_seg_weight", 0.03)  # 字数控制权重
            params["eng_smoothproc"] = kwargs.get("eng_smoothproc", True)   # 开启顺滑
            params["eng_colloqproc"] = kwargs.get("eng_colloqproc", True)   # 开启口语规整
            print(f"🎯 启用句子级别分割: 最大{params['eng_seg_max']}字符/句")

        # 发送上传请求
        response = requests.post(UPLOAD_URL, params=params, timeout=30)

        if response.status_code != 200:
            print(f"❌ 上传失败: HTTP {response.status_code}")
            return None

        result = response.json()

        if result.get("code") == "000000":
            order_id = result["content"]["orderId"]
            estimate_time = result["content"].get("taskEstimateTime", 0)
            print(f"✅ 上传成功！")
            print(f"📋 订单ID: {order_id}")
            print(f"⏱️ 预估耗时: {estimate_time/1000:.1f}秒")
            return order_id
        else:
            error_code = result.get('code')
            error_desc = result.get('descInfo')
            print(f"❌ 上传失败: {error_code} - {error_desc}")

            # 提供具体的错误解决方案
            if error_code == "26625":
                print("💡 解决方案:")
                print("   1. 访问 https://console.xfyun.cn/services/lfasr")
                print("   2. 领取新用户免费时长（最多50小时）")
                print("   3. 或者购买语音转写服务时长")
                print("   4. 免费时长获取步骤：")
                print("      - 登录讯飞开放平台控制台")
                print("      - 进入语音转写服务页面")
                print("      - 点击'免费领取'按钮")
            elif error_code == "26601":
                print("💡 请检查APPID是否正确")
            elif error_code == "26610":
                print("💡 请检查请求参数是否正确")
            elif error_code == "26634":
                print("💡 音频文件下载失败，请检查URL是否可访问")

            return None

    except Exception as e:
        print(f"❌ 上传失败: {e}")
        return None


def query_result(order_id, max_retries=20, retry_interval=10, query_translation=False):
    """
    查询转写结果

    Args:
        order_id: 订单ID
        max_retries: 最大重试次数
        retry_interval: 重试间隔(秒)
        query_translation: 是否查询翻译结果
    """
    result_type = "translate" if query_translation else "transfer"
    print(f"🔍 开始查询{'翻译' if query_translation else '转写'}结果...")
    print(f"📋 订单ID: {order_id}")

    for attempt in range(max_retries):
        try:
            # 生成时间戳和签名
            ts = int(time.time())
            signa = generate_signa(
                XFYUN_CONFIG["appid"], XFYUN_CONFIG["secret_key"], ts)

            # 准备查询参数
            params = {
                "appId": XFYUN_CONFIG["appid"],
                "ts": ts,
                "signa": signa,
                "orderId": order_id,
                "resultType": result_type  # 查询转写或翻译结果
            }

            # 发送查询请求
            response = requests.get(RESULT_URL, params=params, timeout=30)

            if response.status_code != 200:
                print(f"❌ 查询失败: HTTP {response.status_code}")
                return None

            result = response.json()

            if result.get("code") == "000000":
                order_info = result["content"]["orderInfo"]
                status = order_info["status"]

                if status == 4:  # 转写完成
                    task_type = "翻译" if query_translation else "转写"
                    print(f"✅ {task_type}完成！")

                    if query_translation:
                        # 处理翻译结果
                        trans_result = result["content"].get("transResult", [])
                        return parse_translation_result(trans_result, order_info)
                    else:
                        # 处理转写结果
                        order_result = result["content"]["orderResult"]
                        return parse_xfyun_result(order_result, order_info)

                elif status == -1:  # 转写失败
                    fail_type = order_info.get("failType", 0)
                    task_type = "翻译" if query_translation else "转写"
                    print(f"❌ {task_type}失败: failType={fail_type}")
                    return None
                elif status in [0, 3]:  # 处理中
                    task_type = "翻译" if query_translation else "转写"
                    print(f"⏳ {task_type}进行中... (第{attempt+1}次查询)")
                    time.sleep(retry_interval)
                    continue
                else:
                    print(f"⚠️ 未知状态: {status}")
                    time.sleep(retry_interval)
                    continue
            else:
                print(
                    f"❌ 查询失败: {result.get('code')} - {result.get('descInfo')}")
                return None

        except Exception as e:
            print(f"❌ 查询异常: {e}")
            time.sleep(retry_interval)
            continue

    print(f"❌ 查询超时，已重试{max_retries}次")
    return None


def parse_translation_result(trans_result, order_info):
    """解析讯飞翻译结果"""
    try:
        print("\n" + "=" * 50)
        print("🌐 翻译结果:")
        print("=" * 50)

        translation_segments = []
        full_translation = []

        # 检查翻译结果的数据类型
        if isinstance(trans_result, str):
            # 如果是字符串，尝试解析为JSON
            try:
                parsed_result = json.loads(trans_result)
                if isinstance(parsed_result, list):
                    trans_result = parsed_result
                else:
                    print(trans_result)
                    full_text = trans_result
                    save_translation_results(full_text, order_info, trans_result, [])
                    return full_text
            except json.JSONDecodeError:
                print(trans_result)
                full_text = trans_result
                save_translation_results(full_text, order_info, trans_result, [])
                return full_text

        # 处理列表格式的翻译结果
        if isinstance(trans_result, list):
            # 如果是列表，遍历处理
            for item in trans_result:
                if isinstance(item, dict):
                    seg_id = item.get("segId", "")
                    dst_text = item.get("dst", "")  # 翻译结果
                    begin_time = item.get("bg", 0)  # 开始时间
                    end_time = item.get("ed", 0)    # 结束时间

                    if dst_text.strip():
                        print(f"[{seg_id}] {dst_text}")
                        full_translation.append(dst_text)

                        # 添加翻译段落信息
                        translation_segments.append({
                            "text": dst_text,
                            "start_time": begin_time,
                            "end_time": end_time,
                            "seg_id": seg_id
                        })
                elif isinstance(item, str):
                    # 如果列表中的项目是字符串
                    print(item)
                    full_translation.append(item)
        else:
            print(f"⚠️ 未知的翻译结果格式: {type(trans_result)}")
            print(str(trans_result))
            full_text = str(trans_result)
            save_translation_results(full_text, order_info, trans_result, [])
            return full_text

        print("=" * 50)

        # 合并所有翻译文本
        full_text = " ".join(full_translation)

        # 保存翻译结果
        save_translation_results(full_text, order_info, trans_result, translation_segments)

        return full_text

    except Exception as e:
        print(f"❌ 解析翻译结果失败: {e}")
        print(f"翻译结果类型: {type(trans_result)}")
        print(f"翻译结果内容: {trans_result}")
        return None


def parse_xfyun_result(order_result, order_info):
    """解析讯飞转写结果，支持句子级别分割"""
    try:
        # 解析JSON结果
        result_data = json.loads(order_result)

        # 提取文本内容和时间戳信息
        transcription_lines = []
        subtitle_segments = []

        # 优先使用lattice2（未顺滑结果），包含更精细的分段信息
        if "lattice2" in result_data:
            lattice_data = result_data["lattice2"]
            print(f"📊 使用lattice2数据，包含{len(lattice_data)}个分段")
        else:
            # 使用lattice（顺滑结果）
            lattice_data = result_data.get("lattice", [])
            print(f"📊 使用lattice数据，包含{len(lattice_data)}个分段")

        for item in lattice_data:
            if "json_1best" in item:
                json_1best = item["json_1best"]
                if isinstance(json_1best, str):
                    json_1best = json.loads(json_1best)

                # 提取句子文本和时间信息
                st = json_1best.get("st", {})
                begin_time = int(st.get("bg", 0))  # 开始时间（毫秒）
                end_time = int(st.get("ed", 0))    # 结束时间（毫秒）
                rt = st.get("rt", [])

                # 处理每个句子段落
                for rt_item in rt:
                    ws = rt_item.get("ws", [])
                    sentence_words = []
                    word_timings = []

                    # 收集词语和时间信息
                    for ws_item in ws:
                        wb = ws_item.get("wb", 0)  # 词语开始帧数
                        we = ws_item.get("we", 0)  # 词语结束帧数
                        cw = ws_item.get("cw", [])

                        for cw_item in cw:
                            word = cw_item.get("w", "")
                            wp = cw_item.get("wp", "n")  # 词语属性

                            if word and word.strip():
                                sentence_words.append(word)
                                # 计算词语的绝对时间（帧数*10ms + 句子开始时间）
                                word_start = begin_time + wb * 10
                                word_end = begin_time + we * 10
                                word_timings.append({
                                    "word": word,
                                    "start": word_start,
                                    "end": word_end,
                                    "type": wp
                                })

                    if sentence_words:
                        sentence = "".join(sentence_words)

                        # 按标点符号进一步分割句子
                        sub_sentences = split_by_punctuation(sentence, word_timings, begin_time, end_time)

                        for sub_sentence in sub_sentences:
                            if sub_sentence["text"].strip():
                                transcription_lines.append(sub_sentence["text"])
                                subtitle_segments.append(sub_sentence)

        # 合并所有文本
        full_text = " ".join(transcription_lines)

        print(f"📝 解析完成: {len(subtitle_segments)}个字幕段落")

        # 保存结果
        save_xfyun_results(full_text, order_info, result_data, subtitle_segments)

        return full_text

    except Exception as e:
        print(f"❌ 解析结果失败: {e}")
        return None


def split_by_punctuation(sentence, word_timings, default_start, default_end):
    """按标点符号分割句子，生成更精细的字幕段落"""
    try:
        # 标点符号列表
        punctuation_marks = ['.', '!', '?', ';', '。', '！', '？', '；']

        # 设置最小句子长度限制
        MIN_SENTENCE_LENGTH = 10  # 最少10个字符
        MIN_WORD_COUNT = 3        # 最少3个单词

        sub_sentences = []
        current_sentence = ""
        current_start = default_start
        current_words = []

        for i, timing in enumerate(word_timings):
            word = timing["word"]
            current_sentence += word
            current_words.append(timing)

            # 检查是否遇到标点符号或到达句子末尾
            is_punctuation = any(punct in word for punct in punctuation_marks)
            is_last_word = i == len(word_timings) - 1

            # 只有在满足最小长度要求时才分割
            meets_length_requirement = (
                len(current_sentence.strip()) >= MIN_SENTENCE_LENGTH and
                len(current_words) >= MIN_WORD_COUNT
            )

            if (is_punctuation and meets_length_requirement) or is_last_word:
                if current_sentence.strip():
                    # 计算当前子句的时间范围
                    if current_words:
                        sentence_start = current_words[0]["start"]
                        sentence_end = current_words[-1]["end"]
                    else:
                        sentence_start = current_start
                        sentence_end = default_end

                    sub_sentences.append({
                        "text": current_sentence.strip(),
                        "start_time": sentence_start,
                        "end_time": sentence_end
                    })

                # 重置当前句子
                current_sentence = ""
                current_words = []
                if i < len(word_timings) - 1:
                    current_start = word_timings[i + 1]["start"]
            elif is_punctuation and not meets_length_requirement:
                # 如果遇到标点但不满足长度要求，继续累积
                continue

        # 如果没有生成任何子句，返回整个句子
        if not sub_sentences and sentence.strip():
            sub_sentences.append({
                "text": sentence.strip(),
                "start_time": default_start,
                "end_time": default_end
            })

        # 合并过短的句子
        merged_sentences = merge_short_sentences(sub_sentences, MIN_SENTENCE_LENGTH)

        return merged_sentences

    except Exception as e:
        print(f"⚠️ 句子分割失败: {e}")
        # 返回原始句子
        return [{
            "text": sentence.strip(),
            "start_time": default_start,
            "end_time": default_end
        }]


def merge_short_sentences(sentences, min_length):
    """合并过短的句子"""
    if not sentences:
        return sentences

    merged = []
    current_merged = sentences[0]

    for i in range(1, len(sentences)):
        current_sentence = sentences[i]

        # 如果当前合并的句子太短，继续合并
        if len(current_merged["text"]) < min_length:
            # 合并文本
            current_merged["text"] += " " + current_sentence["text"]
            # 更新结束时间
            current_merged["end_time"] = current_sentence["end_time"]
        else:
            # 当前句子足够长，保存并开始新的合并
            merged.append(current_merged)
            current_merged = current_sentence

    # 添加最后一个句子
    merged.append(current_merged)

    return merged


def save_translation_results(translation_text, order_info, raw_result, translation_segments=None):
    """保存讯飞翻译结果"""
    try:
        # 保存详细结果
        result_data = {
            "translation": translation_text,
            "order_info": order_info,
            "raw_result": raw_result,
            "translation_segments": translation_segments,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "provider": "xfyun",
            "type": "translation"
        }

        with open("xfyun_translation.json", "w", encoding="utf-8") as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)

        # 保存纯文本翻译
        with open("xfyun_translation.txt", "w", encoding="utf-8") as f:
            f.write(translation_text)

        # 生成翻译字幕（基于真实时间戳）
        if translation_segments:
            generate_translation_srt(translation_segments)
            print(f"📄 翻译结果已保存到: xfyun_translation.json")
            print(f"📄 翻译文本已保存到: xfyun_translation.txt")
            print(f"📄 翻译字幕已保存到: xfyun_translation.srt")
        else:
            print(f"📄 翻译结果已保存到: xfyun_translation.json")
            print(f"📄 翻译文本已保存到: xfyun_translation.txt")

    except Exception as e:
        print(f"❌ 保存翻译结果失败: {e}")


def generate_translation_srt(translation_segments):
    """根据翻译段落生成SRT字幕"""
    try:
        srt_content = []

        for i, segment in enumerate(translation_segments):
            if not segment["text"].strip():
                continue

            # 转换时间戳（毫秒转为SRT格式）
            start_time = format_srt_time(segment["start_time"] / 1000)
            end_time = format_srt_time(segment["end_time"] / 1000)

            # 添加SRT条目
            srt_content.append(f"{i+1}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(segment["text"])
            srt_content.append("")

        # 保存SRT文件
        with open("xfyun_translation.srt", "w", encoding="utf-8") as f:
            f.write("\n".join(srt_content))

    except Exception as e:
        print(f"❌ 生成翻译SRT字幕失败: {e}")


def save_xfyun_results(transcription_text, order_info, raw_result, subtitle_segments=None):
    """保存讯飞转写结果"""
    try:
        print("\n" + "=" * 50)
        print("🎯 转写结果:")
        print("=" * 50)
        print(transcription_text)
        print("=" * 50)

        # 保存详细结果
        result_data = {
            "transcription": transcription_text,
            "order_info": order_info,
            "raw_result": raw_result,
            "subtitle_segments": subtitle_segments,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "provider": "xfyun"
        }

        with open("xfyun_result.json", "w", encoding="utf-8") as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)

        # 保存纯文本
        with open("xfyun_transcript.txt", "w", encoding="utf-8") as f:
            f.write(transcription_text)

        # 生成SRT字幕（基于真实时间戳）
        if subtitle_segments:
            generate_srt_from_segments(subtitle_segments)
            print(f"📄 详细结果已保存到: xfyun_result.json")
            print(f"📄 转录文本已保存到: xfyun_transcript.txt")
            print(f"📄 SRT字幕已保存到: xfyun_subtitles.srt")
        else:
            print(f"📄 详细结果已保存到: xfyun_result.json")
            print(f"📄 转录文本已保存到: xfyun_transcript.txt")

    except Exception as e:
        print(f"❌ 保存结果失败: {e}")


def generate_srt_from_segments(subtitle_segments):
    """根据时间戳信息生成SRT字幕"""
    try:
        # 最终合并过短的字幕段落
        MIN_SUBTITLE_LENGTH = 15  # 最小字幕长度
        merged_segments = final_merge_short_segments(subtitle_segments, MIN_SUBTITLE_LENGTH)

        srt_content = []
        subtitle_index = 1

        for segment in merged_segments:
            if not segment["text"].strip():
                continue

            # 转换时间戳（毫秒转为SRT格式）
            start_time = format_srt_time(segment["start_time"] / 1000)
            end_time = format_srt_time(segment["end_time"] / 1000)

            # 添加SRT条目
            srt_content.append(f"{subtitle_index}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(segment["text"])
            srt_content.append("")
            subtitle_index += 1

        # 保存SRT文件
        with open("xfyun_subtitles.srt", "w", encoding="utf-8") as f:
            f.write("\n".join(srt_content))

        print(f"📊 最终生成 {subtitle_index-1} 个字幕段落")

    except Exception as e:
        print(f"❌ 生成SRT字幕失败: {e}")


def final_merge_short_segments(segments, min_length):
    """最终合并过短的字幕段落"""
    if not segments:
        return segments

    merged = []
    current_merged = None

    for segment in segments:
        text = segment["text"].strip()

        # 跳过空文本
        if not text:
            continue

        # 如果当前没有合并的段落
        if current_merged is None:
            current_merged = segment.copy()
        else:
            # 总是尝试合并，除非合并后会太长
            potential_merged_text = current_merged["text"] + " " + text

            # 如果当前段落已经足够长，且新段落也足够长，则分开
            if (len(current_merged["text"]) >= min_length and
                len(text) >= min_length):
                # 两个都足够长，保存当前的并开始新的
                merged.append(current_merged)
                current_merged = segment.copy()
            else:
                # 至少有一个太短，继续合并
                current_merged["text"] = potential_merged_text
                current_merged["end_time"] = segment["end_time"]

    # 添加最后一个段落
    if current_merged and current_merged["text"].strip():
        merged.append(current_merged)
    return merged


def format_srt_time(seconds):
    """格式化SRT时间戳"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    milliseconds = int((seconds % 1) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"





def transcribe_with_xfyun(audio_url=None, language="cn", translate_to=None, **kwargs):
    """
    使用讯飞API进行语音转写的完整流程

    Args:
        audio_url: 音频文件URL
        language: 语言代码
        translate_to: 翻译目标语言
        **kwargs: 其他参数
    """
    if audio_url is None:
        audio_url = AUDIO_URL

    # 1. 上传音频文件
    order_id = upload_audio_file(audio_url, language, translate_to, **kwargs)
    if not order_id:
        return None

    # 2. 查询转写结果
    transcription_result = query_result(order_id)
    if not transcription_result:
        return None

    # 3. 如果启用了翻译，查询翻译结果
    if translate_to and translate_to != language:
        print("\n" + "=" * 50)
        print("🌐 开始查询翻译结果...")
        translation_result = query_result(order_id, query_translation=True)
        return {
            "transcription": transcription_result,
            "translation": translation_result
        }

    return {"transcription": transcription_result}


def demo_without_config():
    """演示功能（无需真实配置）"""
    print("🎙️ 讯飞语音转写工具 - 演示模式")
    print("=" * 50)

    print("📝 这是一个演示，展示如何使用讯飞语音转写API")
    print()

    # 模拟转写结果
    demo_result = """这是一个语音转写的演示。
讯飞语音转写服务支持多种语言和音频格式，
包括中文普通话、英语、日语、韩语等。
转写结果准确率高，支持角色分离和热词功能。"""

    print("🎯 模拟转写结果:")
    print("=" * 50)
    print(demo_result)
    print("=" * 50)

    # 保存演示结果
    result_data = {
        "audio_url": AUDIO_URL,
        "transcription": demo_result,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "provider": "xfyun",
        "note": "这是演示数据，非真实转写结果"
    }

    with open("demo_result.json", "w", encoding="utf-8") as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)

    print("📄 演示文件已生成:")
    print("   - demo_result.json (转写结果)")

    print("\n💡 要使用真实API，请:")
    print("   1. 访问 https://console.xfyun.cn/")
    print("   2. 创建应用并开通语音转写服务")
    print("   3. 获取APPID和SECRET_KEY")
    print("   4. 修改代码中的配置")


def test_different_languages():
    """测试不同语言的转写"""
    languages = [
        ("cn", "中文"),
        ("en", "英文"),
        ("ja", "日语"),
        ("ko", "韩语")
    ]

    print("🌍 测试不同语言转写...")

    for lang_code, lang_name in languages:
        print(f"\n--- 测试语言: {lang_name} ({lang_code}) ---")
        try:
            result = transcribe_with_xfyun(
                audio_url=AUDIO_URL,
                language=lang_code
            )
            if result:
                print(f"✅ {lang_name}转写成功")
            else:
                print(f"❌ {lang_name}转写失败")
        except Exception as e:
            print(f"❌ {lang_name}转写异常: {e}")
        print("-" * 50)


def main():
    """主函数"""
    print("🎙️ 讯飞语音转写工具")
    print("=" * 50)

    # 检查配置
    if XFYUN_CONFIG["appid"] == "your_appid_here":
        print("⚠️ 未检测到讯飞配置，运行演示模式")
        demo_without_config()
        return

    # 基本转写（优化的句子级别分割）
    print("\n1. 英文语音转写（优化的句子级别分割）")
    result = transcribe_with_xfyun(
        audio_url=AUDIO_URL,
        language="en",              # 英文识别
        roleType=1,                 # 开启角色分离
        hotWord="speech|recognition|transcription",  # 英文热词
        eng_seg_max=50,             # 增加到50字符一句，避免过度分割
        eng_seg_min=10,             # 增加到10字符最小长度
        eng_seg_weight=0.02,        # 降低权重，减少强制分割
        eng_smoothproc=True,        # 开启顺滑
        eng_colloqproc=True         # 开启口语规整
    )

    if result:
        print("\n✅ 转写成功！")

        # 英文转写+中文翻译（优化的句子级别）
        print("\n2. 英文语音转写+中文翻译（优化的句子级别分割）")
        translation_result = transcribe_with_xfyun(
            audio_url=AUDIO_URL,
            language="en",              # 英文识别
            translate_to="cn",          # 翻译为中文
            roleType=1,                 # 开启角色分离
            transMode=2,                # 按段落翻译
            eng_seg_max=40,             # 适中的句子长度
            eng_seg_min=8,              # 最小8字符一句
            eng_seg_weight=0.03,        # 适中的权重
            eng_smoothproc=True,        # 开启顺滑
            eng_colloqproc=True         # 开启口语规整
        )

        if translation_result and translation_result.get("translation"):
            print("\n✅ 翻译成功！")
        else:
            print("\n⚠️ 翻译功能可能需要额外开通")

    else:
        print("\n❌ 转写失败，切换到演示模式")
        demo_without_config()
        return

    print("\n🎉 所有任务完成！")


if __name__ == "__main__":
    main()
