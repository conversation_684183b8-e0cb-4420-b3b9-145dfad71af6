#!/usr/bin/env python3
"""
Whisper语音识别使用示例

展示如何使用Whisper进行语音识别
"""

from whisper_speech_recognition import (
    transcribe_audio,
    transcribe_whisper,
    WhisperSpeechRecognizer,
    SUPPORTED_LANGUAGES
)


def example_basic_usage():
    """基本使用示例"""
    print("📝 基本使用示例")
    print("=" * 40)

    # 方式1：使用主要的API
    print("1. 主要的转录函数:")
    print("   result = transcribe_audio(audio_path='audio.wav', language='auto')")

    # 方式2：使用便捷函数
    print("\n2. 便捷的转录函数:")
    print("   result = transcribe_whisper(")
    print("       audio_url='http://example.com/audio.mp3',")
    print("       language='zh',")
    print("       model_size='base'")
    print("   )")


def example_model_selection():
    """模型选择示例"""
    print("\n🧠 模型选择示例")
    print("=" * 40)
    
    recognizer = WhisperSpeechRecognizer()
    
    # 显示可用模型
    models = recognizer.get_available_models()
    print("可用模型:")
    for name, info in list(models.items())[:3]:  # 显示前3个
        print(f"  {name}: {info['size']}, {info['accuracy']}")
    
    # 根据音频时长推荐模型
    durations = [30, 300, 1800]  # 30秒, 5分钟, 30分钟
    print("\n根据音频时长的模型推荐:")
    for duration in durations:
        recommended = recognizer.get_optimal_model_for_duration(duration)
        minutes = duration // 60
        time_str = f"{minutes}分钟" if minutes > 0 else f"{duration}秒"
        print(f"  {time_str}: {recommended}")


def example_language_support():
    """语言支持示例"""
    print("\n🌍 语言支持示例")
    print("=" * 40)

    print("支持的语言:")
    for code, name in list(SUPPORTED_LANGUAGES.items())[:6]:  # 显示前6种
        print(f"  {code}: {name}")
    print(f"  ... 以及其他 {len(SUPPORTED_LANGUAGES)-6} 种语言")

    print("\n使用示例:")
    print("  # 中文音频")
    print("  result = transcribe_audio(audio_path='chinese.wav', language='zh')")
    print("  # 自动检测语言")
    print("  result = transcribe_audio(audio_path='unknown.wav', language='auto')")
    print("  # 英文音频并翻译")
    print("  result = transcribe_audio(audio_path='english.wav', language='en', translate_to='en')")


def example_performance_optimization():
    """性能优化示例"""
    print("\n⚡ 性能优化示例")
    print("=" * 40)
    
    print("1. 设备选择:")
    print("   # 自动选择最优设备")
    print("   recognizer = WhisperSpeechRecognizer(device='auto')")
    print("   # 强制使用GPU")
    print("   recognizer = WhisperSpeechRecognizer(device='cuda')")
    
    print("\n2. 模型缓存:")
    print("   # 预加载常用模型")
    print("   recognizer.preload_models(['tiny', 'base'])")
    print("   # 清理缓存释放内存")
    print("   recognizer.clear_model_cache()")
    
    print("\n3. 批处理:")
    print("   audio_files = ['audio1.wav', 'audio2.wav']")
    print("   for audio in audio_files:")
    print("       result = transcribe_audio(audio_path=audio)")


def example_advanced_usage():
    """高级使用示例"""
    print("\n🔧 高级使用示例")
    print("=" * 40)

    print("1. 使用类接口进行更多控制:")
    print("```python")
    print("recognizer = WhisperSpeechRecognizer(model_size='base', device='auto')")
    print("")
    print("# 获取音频信息")
    print("info = recognizer.get_audio_info('audio.wav')")
    print("print(f'音频时长: {info[\"duration\"]}秒')")
    print("")
    print("# 直接转录")
    print("result = recognizer.transcribe_audio('audio.wav', language='auto')")
    print("```")

    print("\n2. 批量处理和缓存管理:")
    print("```python")
    print("# 预加载模型")
    print("recognizer.preload_models(['tiny', 'base'])")
    print("")
    print("# 批量处理")
    print("for audio_file in audio_files:")
    print("    result = recognizer.transcribe_audio(audio_file)")
    print("    process_result(result)")
    print("")
    print("# 清理缓存")
    print("recognizer.clear_model_cache()")
    print("```")


def example_features():
    """功能特性展示"""
    print("\n🎯 主要功能特性")
    print("=" * 40)

    features = [
        "🔒 隐私保护 - 数据完全本地处理，不上传到服务器",
        "💰 完全免费 - 无API费用，无使用限制",
        "🌐 离线使用 - 无需网络连接，适合离线环境",
        "🎛️ 模型选择 - 7种模型大小，从39MB到1550MB",
        "🚀 实时处理 - tiny/base模型支持实时识别",
        "🌍 多语言 - 支持99种语言，自动检测",
        "⚡ 硬件加速 - 支持CUDA/MPS/CPU多线程",
        "🔧 开源实现 - 基于OpenAI Whisper，可自定义",
        "📊 详细输出 - 提供时间戳、置信度等信息",
        "🎵 格式支持 - 支持MP3/WAV/M4A/FLAC等格式"
    ]

    for feature in features:
        print(f"  {feature}")


def main():
    """主函数"""
    print("🎙️ Whisper语音识别使用示例")
    print("=" * 60)
    
    try:
        example_basic_usage()
        example_model_selection()
        example_language_support()
        example_performance_optimization()
        example_advanced_usage()
        example_features()
        
        print("\n" + "=" * 60)
        print("🎉 示例展示完成！")
        print("💡 您现在可以开始使用Whisper语音识别了")
        print("📚 更多详细信息请查看 README.md")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")


if __name__ == "__main__":
    main()
