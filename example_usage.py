#!/usr/bin/env python3
"""
Whisper语音识别使用示例

展示如何使用新的Whisper实现替换原有的讯飞语音识别
"""

from whisper_speech_recognition import (
    transcribe_with_whisper,
    transcribe_with_xfyun,  # 兼容原有API
    WhisperSpeechRecognizer
)


def example_basic_usage():
    """基本使用示例"""
    print("📝 基本使用示例")
    print("=" * 40)
    
    # 方式1：使用兼容的API（与原有代码完全相同）
    print("1. 兼容原有API的调用方式:")
    print("   result = transcribe_with_xfyun(audio_url='...', language='cn')")
    
    # 方式2：使用新的Whisper API
    print("\n2. 使用新的Whisper API:")
    print("   result = transcribe_with_whisper(")
    print("       audio_url='...',")
    print("       language='cn',")
    print("       model_size='base'")
    print("   )")


def example_model_selection():
    """模型选择示例"""
    print("\n🧠 模型选择示例")
    print("=" * 40)
    
    recognizer = WhisperSpeechRecognizer()
    
    # 显示可用模型
    models = recognizer.get_available_models()
    print("可用模型:")
    for name, info in list(models.items())[:3]:  # 显示前3个
        print(f"  {name}: {info['size']}, {info['accuracy']}")
    
    # 根据音频时长推荐模型
    durations = [30, 300, 1800]  # 30秒, 5分钟, 30分钟
    print("\n根据音频时长的模型推荐:")
    for duration in durations:
        recommended = recognizer.get_optimal_model_for_duration(duration)
        minutes = duration // 60
        time_str = f"{minutes}分钟" if minutes > 0 else f"{duration}秒"
        print(f"  {time_str}: {recommended}")


def example_language_support():
    """语言支持示例"""
    print("\n🌍 语言支持示例")
    print("=" * 40)
    
    # 支持的语言示例
    languages = [
        ("cn", "中文"),
        ("en", "英文"), 
        ("ja", "日语"),
        ("ko", "韩语"),
        ("auto", "自动检测")
    ]
    
    print("支持的语言:")
    for code, name in languages:
        print(f"  {code}: {name}")
    
    print("\n使用示例:")
    print("  # 中文音频")
    print("  result = transcribe_with_whisper(audio_path='chinese.wav', language='cn')")
    print("  # 自动检测语言")
    print("  result = transcribe_with_whisper(audio_path='unknown.wav', language='auto')")


def example_performance_optimization():
    """性能优化示例"""
    print("\n⚡ 性能优化示例")
    print("=" * 40)
    
    print("1. 设备选择:")
    print("   # 自动选择最优设备")
    print("   recognizer = WhisperSpeechRecognizer(device='auto')")
    print("   # 强制使用GPU")
    print("   recognizer = WhisperSpeechRecognizer(device='cuda')")
    
    print("\n2. 模型缓存:")
    print("   # 预加载常用模型")
    print("   recognizer.preload_models(['tiny', 'base'])")
    print("   # 清理缓存释放内存")
    print("   recognizer.clear_model_cache()")
    
    print("\n3. 批处理:")
    print("   audio_files = ['audio1.wav', 'audio2.wav']")
    print("   for audio in audio_files:")
    print("       result = transcribe_with_whisper(audio_path=audio)")


def example_migration_guide():
    """迁移指南示例"""
    print("\n🔄 迁移指南")
    print("=" * 40)
    
    print("原有代码（讯飞API）:")
    print("```python")
    print("from test import transcribe_with_xfyun")
    print("")
    print("result = transcribe_with_xfyun(")
    print("    audio_url='https://example.com/audio.mp3',")
    print("    language='cn',")
    print("    translate_to='en'")
    print(")")
    print("```")
    
    print("\n新代码（Whisper实现，完全兼容）:")
    print("```python")
    print("from whisper_speech_recognition import transcribe_with_xfyun")
    print("")
    print("# 完全相同的调用方式，无需修改任何代码")
    print("result = transcribe_with_xfyun(")
    print("    audio_url='https://example.com/audio.mp3',")
    print("    language='cn',")
    print("    translate_to='en'")
    print(")")
    print("```")
    
    print("\n可选：使用新功能:")
    print("```python")
    print("from whisper_speech_recognition import transcribe_with_whisper")
    print("")
    print("result = transcribe_with_whisper(")
    print("    audio_url='https://example.com/audio.mp3',")
    print("    language='cn',")
    print("    model_size='base',  # 新功能：选择模型")
    print("    translate_to='en'")
    print(")")
    print("```")


def example_advantages():
    """优势展示"""
    print("\n🎯 主要优势")
    print("=" * 40)
    
    advantages = [
        "🔒 隐私保护 - 数据完全本地处理",
        "💰 成本效益 - 免费使用，无API费用", 
        "🌐 离线使用 - 无需网络连接",
        "🎛️ 模型选择 - 7种模型大小可选",
        "🚀 实时处理 - 小模型支持实时识别",
        "🌍 语言支持 - 99种语言自动检测",
        "⚡ GPU加速 - 支持CUDA/MPS加速",
        "🔧 可定制性 - 开源实现，可自定义"
    ]
    
    for advantage in advantages:
        print(f"  {advantage}")


def main():
    """主函数"""
    print("🎙️ Whisper语音识别使用示例")
    print("=" * 60)
    
    try:
        example_basic_usage()
        example_model_selection()
        example_language_support()
        example_performance_optimization()
        example_migration_guide()
        example_advantages()
        
        print("\n" + "=" * 60)
        print("🎉 示例展示完成！")
        print("💡 您现在可以开始使用Whisper语音识别了")
        print("📚 更多详细信息请查看 README.md")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")


if __name__ == "__main__":
    main()
